import traceback
import pandas as pd
import logging
from fastapi import APIRouter, HTTPException, Request, Depends
from datetime import datetime, timedelta
from app.utils.dependencies import get_vanna, get_cache

# Create router with single tag
router = APIRouter(tags=["dashboard"])
logger = logging.getLogger(__name__)

@router.get("/dashboard/test")
async def test_endpoint():
    """Test endpoint to verify dashboard API is working"""
    return {
        "status": "success",
        "message": "Dashboard API is working",
        "data": {"test": True}
    }

@router.get("/dashboard/overview")
async def get_overview_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get overview statistics for the dashboard"""
    try:
        # Total patients
        total_patients_df = vn.run_sql("SELECT COUNT(*) as total_patients FROM Patients")
        total_patients = int(total_patients_df.iloc[0]['total_patients'])

        # Total consultations
        total_consultations_df = vn.run_sql("SELECT COUNT(*) as total_consultations FROM Consult_prob")
        total_consultations = int(total_consultations_df.iloc[0]['total_consultations'])

        # Total doctors
        total_doctors_df = vn.run_sql("SELECT COUNT(*) as total_doctors FROM Medecins")
        total_doctors = int(total_doctors_df.iloc[0]['total_doctors'])

        # Recent consultations (last 30 days)
        recent_consultations_df = vn.run_sql("""
            SELECT COUNT(*) as recent_consultations
            FROM Consult_prob
            WHERE DateConsultation >= NOW() - INTERVAL '30 days'
        """)
        recent_consultations = int(recent_consultations_df.iloc[0]['recent_consultations'])

        return {
            "status": "success",
            "data": {
                "total_patients": total_patients,
                "total_consultations": total_consultations,
                "total_doctors": total_doctors,
                "recent_consultations": recent_consultations
            }
        }

    except Exception as e:
        logger.error(f"Error getting overview stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/patients")
async def get_patient_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get patient demographics and statistics"""
    try:
        # Gender distribution
        gender_df = vn.run_sql("""
            SELECT
                CASE
                    WHEN Sexe = 'M' THEN 'Male'
                    WHEN Sexe = 'F' THEN 'Female'
                    ELSE 'Unknown'
                END as gender,
                COUNT(*) as count
            FROM Patients
            GROUP BY Sexe
        """)

        # Age distribution
        age_df = vn.run_sql("""
            SELECT
                CASE
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) < 18 THEN 'Under 18'
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) BETWEEN 18 AND 30 THEN '18-30'
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) BETWEEN 31 AND 50 THEN '31-50'
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) BETWEEN 51 AND 70 THEN '51-70'
                    ELSE 'Over 70'
                END as age_group,
                COUNT(*) as count
            FROM Patients
            WHERE DateNaissance IS NOT NULL
            GROUP BY age_group
            ORDER BY age_group
        """)

        return {
            "status": "success",
            "data": {
                "gender_distribution": gender_df.to_dict('records'),
                "age_distribution": age_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting patient stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/consultations")
async def get_consultation_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get consultation statistics and trends"""
    try:
        # Consultations by month (last 12 months)
        monthly_consultations_df = vn.run_sql("""
            SELECT
                TO_CHAR(DateConsultation, 'YYYY-MM') as month,
                COUNT(*) as count
            FROM Consult_prob
            WHERE DateConsultation >= NOW() - INTERVAL '12 months'
            GROUP BY TO_CHAR(DateConsultation, 'YYYY-MM')
            ORDER BY month
        """)

        # Consultations by doctor specialty
        specialty_df = vn.run_sql("""
            SELECT
                COALESCE(m.Specialite, 'Unknown') as specialty,
                COUNT(*) as count
            FROM Consult_prob cp
            LEFT JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            GROUP BY m.Specialite
            ORDER BY count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "monthly_consultations": monthly_consultations_df.to_dict('records'),
                "consultations_by_specialty": specialty_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting consultation stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/diagnoses")
async def get_diagnosis_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get diagnosis statistics"""
    try:
        # Most common diagnoses
        diagnosis_df = vn.run_sql("""
            SELECT
                Libelle as diagnosis,
                COUNT(*) as count
            FROM Diagnostics
            GROUP BY Libelle
            ORDER BY count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "common_diagnoses": diagnosis_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting diagnosis stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/biometrics")
async def get_biometric_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get biometric statistics and trends"""
    try:
        # Average vital signs
        vitals_df = vn.run_sql("""
            SELECT
                ROUND(AVG(Temperature), 1) as avg_temperature,
                ROUND(AVG(TensionSystolique), 0) as avg_systolic,
                ROUND(AVG(TensionDiastolique), 0) as avg_diastolic,
                ROUND(AVG(Pouls), 0) as avg_pulse,
                ROUND(AVG(SpO2), 0) as avg_spo2,
                ROUND(AVG(FrequenceResp), 0) as avg_resp_rate
            FROM Biometrie
            WHERE Temperature IS NOT NULL
               OR TensionSystolique IS NOT NULL
               OR TensionDiastolique IS NOT NULL
               OR Pouls IS NOT NULL
               OR SpO2 IS NOT NULL
               OR FrequenceResp IS NOT NULL
        """)

        return {
            "status": "success",
            "data": {
                "average_vitals": vitals_df.to_dict('records')[0] if not vitals_df.empty else {}
            }
        }

    except Exception as e:
        logger.error(f"Error getting biometric stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

# Frontend-expected endpoints (aliases for existing functionality)
@router.get("/dashboard/patient-demographics")
async def get_patient_demographics(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get patient demographics - alias for /dashboard/patients"""
    return await get_patient_stats(request, vn)


@router.get("/dashboard/chat/overview")
async def get_chat_overview_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get chat overview statistics"""
    try:
        # Total chat sessions
        total_sessions_df = vn.run_sql("""
            SELECT COUNT(*) as total_sessions
            FROM conversation
        """)

        # Total messages
        total_messages_df = vn.run_sql("""
            SELECT COUNT(*) as total_messages
            FROM chat_history
            WHERE is_hidden = false
        """)

        # Active users (users who have chatted in last 30 days)
        active_users_df = vn.run_sql("""
            SELECT COUNT(DISTINCT user_id) as active_users
            FROM conversation
            WHERE last_activity >= NOW() - INTERVAL '30 days'
        """)

        # Average messages per session
        avg_messages_df = vn.run_sql("""
            SELECT
                ROUND(AVG(message_count), 1) as avg_messages_per_session
            FROM (
                SELECT
                    conversation_id,
                    COUNT(*) as message_count
                FROM chat_history
                WHERE is_hidden = false
                GROUP BY conversation_id
            ) session_stats
        """)

        return {
            "status": "success",
            "data": {
                "total_sessions": total_sessions_df.iloc[0]['total_sessions'] if not total_sessions_df.empty else 0,
                "total_messages": total_messages_df.iloc[0]['total_messages'] if not total_messages_df.empty else 0,
                "active_users": active_users_df.iloc[0]['active_users'] if not active_users_df.empty else 0,
                "avg_messages_per_session": avg_messages_df.iloc[0]['avg_messages_per_session'] if not avg_messages_df.empty else 0
            }
        }

    except Exception as e:
        logger.error(f"Error getting chat overview stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})


@router.get("/dashboard/chat/activity")
async def get_chat_activity_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get chat activity trends"""
    try:
        # Messages by day (last 30 days)
        daily_activity_df = vn.run_sql("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as message_count,
                COUNT(DISTINCT conversation_id) as session_count
            FROM chat_history
            WHERE created_at >= NOW() - INTERVAL '30 days'
                AND is_hidden = false
            GROUP BY DATE(created_at)
            ORDER BY date
        """)

        # Messages by hour of day
        hourly_activity_df = vn.run_sql("""
            SELECT
                EXTRACT(HOUR FROM created_at) as hour,
                COUNT(*) as message_count
            FROM chat_history
            WHERE created_at >= NOW() - INTERVAL '7 days'
                AND is_hidden = false
            GROUP BY EXTRACT(HOUR FROM created_at)
            ORDER BY hour
        """)

        # Message type distribution
        message_types_df = vn.run_sql("""
            SELECT
                content_type,
                COUNT(*) as count
            FROM chat_history
            WHERE is_hidden = false
            GROUP BY content_type
            ORDER BY count DESC
        """)

        return {
            "status": "success",
            "data": {
                "daily_activity": daily_activity_df.to_dict('records') if not daily_activity_df.empty else [],
                "hourly_activity": hourly_activity_df.to_dict('records') if not hourly_activity_df.empty else [],
                "message_types": message_types_df.to_dict('records') if not message_types_df.empty else []
            }
        }

    except Exception as e:
        logger.error(f"Error getting chat activity stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})


@router.get("/dashboard/chat/topics")
async def get_chat_topics_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get chat topics and categories statistics"""
    try:
        # Topic usage statistics
        topic_usage_df = vn.run_sql("""
            SELECT
                ct.name as topic_name,
                ct.description,
                ct.color,
                COUNT(ch.id) as message_count,
                COUNT(DISTINCT ch.conversation_id) as session_count
            FROM chat_topic ct
            LEFT JOIN chat_history ch ON ct.id = ch.topic_id
            GROUP BY ct.id, ct.name, ct.description, ct.color
            ORDER BY message_count DESC
        """)

        # Recent topic trends (last 7 days)
        recent_topics_df = vn.run_sql("""
            SELECT
                ct.name as topic_name,
                DATE(ch.created_at) as date,
                COUNT(*) as daily_count
            FROM chat_topic ct
            JOIN chat_history ch ON ct.id = ch.topic_id
            WHERE ch.created_at >= NOW() - INTERVAL '7 days'
                AND ch.is_hidden = false
            GROUP BY ct.name, DATE(ch.created_at)
            ORDER BY date, daily_count DESC
        """)

        # Sessions without topics
        untopiced_sessions_df = vn.run_sql("""
            SELECT COUNT(DISTINCT conversation_id) as untopiced_sessions
            FROM chat_history
            WHERE topic_id IS NULL
        """)

        return {
            "status": "success",
            "data": {
                "topic_usage": topic_usage_df.to_dict('records') if not topic_usage_df.empty else [],
                "recent_trends": recent_topics_df.to_dict('records') if not recent_topics_df.empty else [],
                "untopiced_sessions": untopiced_sessions_df.iloc[0]['untopiced_sessions'] if not untopiced_sessions_df.empty else 0
            }
        }

    except Exception as e:
        logger.error(f"Error getting chat topics stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})


@router.get("/dashboard/chat/engagement")
async def get_chat_engagement_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get user engagement and feedback statistics"""
    try:
        # User engagement metrics
        engagement_df = vn.run_sql("""
            SELECT
                COUNT(DISTINCT c.user_id) as total_users,
                AVG(session_length.duration_minutes) as avg_session_duration,
                AVG(session_length.message_count) as avg_messages_per_session
            FROM conversation c
            JOIN (
                SELECT
                    conversation_id,
                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/60 as duration_minutes,
                    COUNT(*) as message_count
                FROM chat_history
                WHERE is_hidden = false
                GROUP BY conversation_id
            ) session_length ON c.id = session_length.conversation_id
        """)

        # Feedback statistics
        feedback_df = vn.run_sql("""
            SELECT
                COUNT(*) as total_feedback,
                SUM(CASE WHEN is_liked THEN 1 ELSE 0 END) as positive_feedback,
                ROUND(
                    (SUM(CASE WHEN is_liked THEN 1 ELSE 0 END)::FLOAT / COUNT(*)) * 100,
                    1
                ) as satisfaction_rate
            FROM feedback
        """)

        # Most active users
        active_users_df = vn.run_sql("""
            SELECT
                u.username,
                u.first_name,
                u.last_name,
                COUNT(DISTINCT c.id) as session_count,
                COUNT(ch.id) as message_count,
                MAX(c.last_activity) as last_activity
            FROM "user" u
            JOIN conversation c ON u.id = c.user_id
            JOIN chat_history ch ON c.id = ch.conversation_id
            WHERE ch.is_hidden = false
            GROUP BY u.id, u.username, u.first_name, u.last_name
            ORDER BY message_count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "engagement": engagement_df.to_dict('records')[0] if not engagement_df.empty else {},
                "feedback": feedback_df.to_dict('records')[0] if not feedback_df.empty else {},
                "active_users": active_users_df.to_dict('records') if not active_users_df.empty else []
            }
        }

    except Exception as e:
        logger.error(f"Error getting chat engagement stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/consultation-trends")
async def get_consultation_trends(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get consultation trends - alias for /dashboard/consultations"""
    return await get_consultation_stats(request, vn)

@router.get("/dashboard/department-stats")
async def get_department_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get department statistics - based on doctor specialties"""
    try:
        # Department statistics based on doctor specialties
        specialty_stats_df = vn.run_sql("""
            SELECT
                COALESCE(m.Specialite, 'Unknown') as department,
                COUNT(DISTINCT m.ID_medecin) as doctor_count,
                COUNT(cp.ID_patient) as consultation_count,
                ROUND(AVG(EXTRACT(YEAR FROM AGE(p.DateNaissance))), 1) as avg_patient_age
            FROM Medecins m
            LEFT JOIN Consult_prob cp ON m.ID_medecin = cp.ID_medecin
            LEFT JOIN Patients p ON cp.ID_patient = p.ID_patient
            GROUP BY m.Specialite
            ORDER BY consultation_count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "department_statistics": specialty_stats_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting department stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/recent-activity")
async def get_recent_activity(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get recent activity feed"""
    try:
        # Recent consultations
        recent_activity_df = vn.run_sql("""
            SELECT
                'consultation' as activity_type,
                CONCAT('Consultation with Dr. ', COALESCE(m.Nom, 'Unknown'), ' for patient ', p.Nom) as description,
                cp.DateConsultation as timestamp,
                'fas fa-stethoscope' as icon
            FROM Consult_prob cp
            LEFT JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            LEFT JOIN Patients p ON cp.ID_patient = p.ID_patient
            WHERE cp.DateConsultation IS NOT NULL
            ORDER BY cp.DateConsultation DESC
            LIMIT 20
        """)

        # Format timestamps for frontend
        activities = []
        for _, row in recent_activity_df.iterrows():
            activities.append({
                "activity_type": row['activity_type'],
                "description": row['description'],
                "timestamp": row['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if pd.notna(row['timestamp']) else 'Unknown',
                "icon": row['icon']
            })

        return {
            "status": "success",
            "data": activities
        }

    except Exception as e:
        logger.error(f"Error getting recent activity: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})
