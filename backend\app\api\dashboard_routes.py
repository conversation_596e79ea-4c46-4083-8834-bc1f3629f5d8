import traceback
import pandas as pd
import logging
from fastapi import APIRouter, HTTPException, Request, Depends
from datetime import datetime, timedelta
from app.utils.dependencies import get_vanna, get_cache

# Create router with single tag
router = APIRouter(tags=["dashboard"])
logger = logging.getLogger(__name__)

@router.get("/dashboard/test")
async def test_endpoint():
    """Test endpoint to verify dashboard API is working"""
    return {
        "status": "success",
        "message": "Dashboard API is working",
        "data": {"test": True}
    }

@router.get("/dashboard/overview")
async def get_overview_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get overview statistics for the dashboard"""
    try:
        # Total patients
        total_patients_df = vn.run_sql("SELECT COUNT(*) as total_patients FROM Patients")
        total_patients = int(total_patients_df.iloc[0]['total_patients'])

        # Total consultations
        total_consultations_df = vn.run_sql("SELECT COUNT(*) as total_consultations FROM Consult_prob")
        total_consultations = int(total_consultations_df.iloc[0]['total_consultations'])

        # Total doctors
        total_doctors_df = vn.run_sql("SELECT COUNT(*) as total_doctors FROM Medecins")
        total_doctors = int(total_doctors_df.iloc[0]['total_doctors'])

        # Recent consultations (last 30 days)
        recent_consultations_df = vn.run_sql("""
            SELECT COUNT(*) as recent_consultations
            FROM Consult_prob
            WHERE DateConsultation >= NOW() - INTERVAL '30 days'
        """)
        recent_consultations = int(recent_consultations_df.iloc[0]['recent_consultations'])

        return {
            "status": "success",
            "data": {
                "total_patients": total_patients,
                "total_consultations": total_consultations,
                "total_doctors": total_doctors,
                "recent_consultations": recent_consultations
            }
        }

    except Exception as e:
        logger.error(f"Error getting overview stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/patients")
async def get_patient_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get patient demographics and statistics"""
    try:
        # Gender distribution
        gender_df = vn.run_sql("""
            SELECT
                CASE
                    WHEN Sexe = 'M' THEN 'Male'
                    WHEN Sexe = 'F' THEN 'Female'
                    ELSE 'Unknown'
                END as gender,
                COUNT(*) as count
            FROM Patients
            GROUP BY Sexe
        """)

        # Age distribution
        age_df = vn.run_sql("""
            SELECT
                CASE
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) < 18 THEN 'Under 18'
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) BETWEEN 18 AND 30 THEN '18-30'
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) BETWEEN 31 AND 50 THEN '31-50'
                    WHEN EXTRACT(YEAR FROM AGE(DateNaissance)) BETWEEN 51 AND 70 THEN '51-70'
                    ELSE 'Over 70'
                END as age_group,
                COUNT(*) as count
            FROM Patients
            WHERE DateNaissance IS NOT NULL
            GROUP BY age_group
            ORDER BY age_group
        """)

        return {
            "status": "success",
            "data": {
                "gender_distribution": gender_df.to_dict('records'),
                "age_distribution": age_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting patient stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/consultations")
async def get_consultation_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get consultation statistics and trends"""
    try:
        # Consultations by month (last 12 months)
        monthly_consultations_df = vn.run_sql("""
            SELECT
                TO_CHAR(DateConsultation, 'YYYY-MM') as month,
                COUNT(*) as count
            FROM Consult_prob
            WHERE DateConsultation >= NOW() - INTERVAL '12 months'
            GROUP BY TO_CHAR(DateConsultation, 'YYYY-MM')
            ORDER BY month
        """)

        # Consultations by doctor specialty
        specialty_df = vn.run_sql("""
            SELECT
                COALESCE(m.Specialite, 'Unknown') as specialty,
                COUNT(*) as count
            FROM Consult_prob cp
            LEFT JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            GROUP BY m.Specialite
            ORDER BY count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "monthly_consultations": monthly_consultations_df.to_dict('records'),
                "consultations_by_specialty": specialty_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting consultation stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/diagnoses")
async def get_diagnosis_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get diagnosis statistics"""
    try:
        # Most common diagnoses
        diagnosis_df = vn.run_sql("""
            SELECT
                Libelle as diagnosis,
                COUNT(*) as count
            FROM Diagnostics
            GROUP BY Libelle
            ORDER BY count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "common_diagnoses": diagnosis_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting diagnosis stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/biometrics")
async def get_biometric_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get biometric statistics and trends"""
    try:
        # Average vital signs
        vitals_df = vn.run_sql("""
            SELECT
                ROUND(AVG(Temperature), 1) as avg_temperature,
                ROUND(AVG(TensionSystolique), 0) as avg_systolic,
                ROUND(AVG(TensionDiastolique), 0) as avg_diastolic,
                ROUND(AVG(Pouls), 0) as avg_pulse,
                ROUND(AVG(SpO2), 0) as avg_spo2,
                ROUND(AVG(FrequenceResp), 0) as avg_resp_rate
            FROM Biometrie
            WHERE Temperature IS NOT NULL
               OR TensionSystolique IS NOT NULL
               OR TensionDiastolique IS NOT NULL
               OR Pouls IS NOT NULL
               OR SpO2 IS NOT NULL
               OR FrequenceResp IS NOT NULL
        """)

        return {
            "status": "success",
            "data": {
                "average_vitals": vitals_df.to_dict('records')[0] if not vitals_df.empty else {}
            }
        }

    except Exception as e:
        logger.error(f"Error getting biometric stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

# Frontend-expected endpoints (aliases for existing functionality)
@router.get("/dashboard/patient-demographics")
async def get_patient_demographics(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get patient demographics - alias for /dashboard/patients"""
    return await get_patient_stats(request, vn)

@router.get("/dashboard/consultation-trends")
async def get_consultation_trends(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get consultation trends - alias for /dashboard/consultations"""
    return await get_consultation_stats(request, vn)

@router.get("/dashboard/department-stats")
async def get_department_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get department statistics - based on doctor specialties"""
    try:
        # Department statistics based on doctor specialties
        specialty_stats_df = vn.run_sql("""
            SELECT
                COALESCE(m.Specialite, 'Unknown') as department,
                COUNT(DISTINCT m.ID_medecin) as doctor_count,
                COUNT(cp.ID_patient) as consultation_count,
                ROUND(AVG(EXTRACT(YEAR FROM AGE(p.DateNaissance))), 1) as avg_patient_age
            FROM Medecins m
            LEFT JOIN Consult_prob cp ON m.ID_medecin = cp.ID_medecin
            LEFT JOIN Patients p ON cp.ID_patient = p.ID_patient
            GROUP BY m.Specialite
            ORDER BY consultation_count DESC
            LIMIT 10
        """)

        return {
            "status": "success",
            "data": {
                "department_statistics": specialty_stats_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting department stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/chat-stats")
async def get_chat_stats(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get chat statistics and analytics"""
    try:
        # Total chat sessions
        total_sessions_df = vn.run_sql("SELECT COUNT(*) as total_sessions FROM conversation")
        total_sessions = int(total_sessions_df.iloc[0]['total_sessions'])

        # Total messages
        total_messages_df = vn.run_sql("SELECT COUNT(*) as total_messages FROM chat_history")
        total_messages = int(total_messages_df.iloc[0]['total_messages'])

        # Active users (users who have sent messages in last 30 days)
        active_users_df = vn.run_sql("""
            SELECT COUNT(DISTINCT user_id) as active_users
            FROM chat_history
            WHERE created_at >= NOW() - INTERVAL '30 days'
        """)
        active_users = int(active_users_df.iloc[0]['active_users'])

        # Recent sessions (last 30 days)
        recent_sessions_df = vn.run_sql("""
            SELECT COUNT(*) as recent_sessions
            FROM conversation
            WHERE started_at >= NOW() - INTERVAL '30 days'
        """)
        recent_sessions = int(recent_sessions_df.iloc[0]['recent_sessions'])

        # Messages by type distribution
        message_types_df = vn.run_sql("""
            SELECT
                message_type,
                COUNT(*) as count
            FROM chat_history
            GROUP BY message_type
            ORDER BY count DESC
        """)

        # Content types distribution
        content_types_df = vn.run_sql("""
            SELECT
                content_type,
                COUNT(*) as count
            FROM chat_history
            GROUP BY content_type
            ORDER BY count DESC
        """)

        # Daily chat activity (last 30 days)
        daily_activity_df = vn.run_sql("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as message_count,
                COUNT(DISTINCT user_id) as unique_users
            FROM chat_history
            WHERE created_at >= NOW() - INTERVAL '30 days'
            GROUP BY DATE(created_at)
            ORDER BY date
        """)

        # Popular topics (if topics are being used)
        topics_df = vn.run_sql("""
            SELECT
                COALESCE(t.name, 'No Topic') as topic_name,
                COUNT(ch.id) as message_count
            FROM chat_history ch
            LEFT JOIN topic t ON ch.topic_id = t.id
            GROUP BY t.name
            ORDER BY message_count DESC
            LIMIT 10
        """)

        # Average session length (in messages)
        session_length_df = vn.run_sql("""
            SELECT
                ROUND(AVG(message_count), 1) as avg_session_length
            FROM (
                SELECT
                    conversation_id,
                    COUNT(*) as message_count
                FROM chat_history
                GROUP BY conversation_id
            ) session_stats
        """)
        avg_session_length = float(session_length_df.iloc[0]['avg_session_length']) if not session_length_df.empty else 0

        return {
            "status": "success",
            "data": {
                "total_sessions": total_sessions,
                "total_messages": total_messages,
                "active_users": active_users,
                "recent_sessions": recent_sessions,
                "avg_session_length": avg_session_length,
                "message_types": message_types_df.to_dict('records'),
                "content_types": content_types_df.to_dict('records'),
                "daily_activity": daily_activity_df.to_dict('records'),
                "popular_topics": topics_df.to_dict('records')
            }
        }

    except Exception as e:
        logger.error(f"Error getting chat stats: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})

@router.get("/dashboard/recent-activity")
async def get_recent_activity(
    request: Request,
    vn = Depends(get_vanna)
):
    """Get recent activity feed"""
    try:
        # Recent consultations
        recent_activity_df = vn.run_sql("""
            SELECT
                'consultation' as activity_type,
                CONCAT('Consultation with Dr. ', COALESCE(m.Nom, 'Unknown'), ' for patient ', p.Nom) as description,
                cp.DateConsultation as timestamp,
                'fas fa-stethoscope' as icon
            FROM Consult_prob cp
            LEFT JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            LEFT JOIN Patients p ON cp.ID_patient = p.ID_patient
            WHERE cp.DateConsultation IS NOT NULL
            ORDER BY cp.DateConsultation DESC
            LIMIT 20
        """)

        # Format timestamps for frontend
        activities = []
        for _, row in recent_activity_df.iterrows():
            activities.append({
                "activity_type": row['activity_type'],
                "description": row['description'],
                "timestamp": row['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if pd.notna(row['timestamp']) else 'Unknown',
                "icon": row['icon']
            })

        return {
            "status": "success",
            "data": activities
        }

    except Exception as e:
        logger.error(f"Error getting recent activity: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})
