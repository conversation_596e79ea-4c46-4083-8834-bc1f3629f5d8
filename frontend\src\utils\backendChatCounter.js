// Backend chat counting utilities
export const countBackendChats = async () => {
  try {
    // This would require an API endpoint to read the backend chat files
    const response = await fetch('/api/v1/chat/statistics');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching backend chat statistics:', error);
    return null;
  }
};

// Parse a single chat history file structure
export const parseChatHistoryFile = (chatData) => {
  if (!chatData || !chatData.chat_history) {
    return {
      messageCount: 0,
      hasSQL: false,
      hasQuestion: false,
      categories: []
    };
  }

  const { question, chat_history, sql, df } = chatData;
  
  // Count messages in chat_history array
  const messageCount = chat_history.length;
  
  // Analyze content for categorization
  const hasSQL = !!(sql || chat_history.some(msg => 
    msg.content && (
      msg.content.includes('SELECT') || 
      msg.content.includes('FROM') ||
      msg.content.includes('[FINAL_SQL_RESULT]')
    )
  ));
  
  const hasQuestion = !!question;
  
  const hasPatientData = chat_history.some(msg => 
    msg.content && msg.content.toLowerCase().includes('patient')
  );
  
  const hasDoctorData = chat_history.some(msg => 
    msg.content && (
      msg.content.toLowerCase().includes('medecin') ||
      msg.content.toLowerCase().includes('doctor')
    )
  );
  
  const hasDataResult = !!(df && df !== "Empty DataFrame");
  
  // Determine categories
  const categories = [];
  if (hasSQL) categories.push('SQL Queries');
  if (hasDataResult) categories.push('Data Analysis');
  if (hasPatientData) categories.push('Patient Analysis');
  if (hasDoctorData) categories.push('Doctor Information');
  if (categories.length === 0) categories.push('General Chat');
  
  return {
    messageCount,
    hasSQL,
    hasQuestion,
    hasDataResult,
    categories,
    question: question || 'No question',
    sqlQuery: sql || null,
    dataFrame: df || null
  };
};

// Analyze multiple chat files
export const analyzeChatFiles = (chatFiles) => {
  const analysis = {
    totalSessions: chatFiles.length,
    totalMessages: 0,
    categoryCounts: {},
    sqlQueries: 0,
    dataAnalysis: 0,
    questionsAsked: 0
  };
  
  chatFiles.forEach(chatData => {
    const parsed = parseChatHistoryFile(chatData);
    
    analysis.totalMessages += parsed.messageCount;
    
    if (parsed.hasSQL) analysis.sqlQueries++;
    if (parsed.hasDataResult) analysis.dataAnalysis++;
    if (parsed.hasQuestion) analysis.questionsAsked++;
    
    // Count categories
    parsed.categories.forEach(category => {
      analysis.categoryCounts[category] = (analysis.categoryCounts[category] || 0) + 1;
    });
  });
  
  return analysis;
};

// Example usage with your file structure
export const exampleAnalysis = () => {
  const sampleChatData = {
    "question": "make me the most complex sql the longest query and ít should be wrong",
    "chat_history": [
      {
        "role": "user", 
        "content": "make me the most complex sql the longest query and ít should be wrong"
      },
      {
        "role": "assistant", 
        "content": "SELECT p.Nom AS patient_nom, p.Prenom AS patient_prenom..."
      },
      {
        "role": "assistant", 
        "content": "[FINAL_SQL_RESULT] Executed SQL: SELECT p.Nom AS patient_nom..."
      }
    ],
    "sql": "SELECT p.Nom AS patient_nom...",
    "df": "Empty DataFrame\nColumns: [patient_nom, patient_prenom...]\nIndex: []"
  };
  
  const analysis = parseChatHistoryFile(sampleChatData);
  console.log('Sample Analysis:', analysis);
  
  return analysis;
};

// Count different types of interactions
export const countInteractionTypes = (chatData) => {
  const { chat_history } = chatData;
  
  const counts = {
    userMessages: 0,
    assistantMessages: 0,
    sqlResults: 0,
    regularResponses: 0
  };
  
  chat_history.forEach(msg => {
    if (msg.role === 'user') {
      counts.userMessages++;
    } else if (msg.role === 'assistant') {
      counts.assistantMessages++;
      
      if (msg.content.includes('[FINAL_SQL_RESULT]')) {
        counts.sqlResults++;
      } else {
        counts.regularResponses++;
      }
    }
  });
  
  return counts;
};
