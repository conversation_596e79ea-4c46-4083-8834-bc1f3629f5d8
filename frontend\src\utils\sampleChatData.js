// Sample chat data for demonstration purposes
export const generateSampleChatSessions = () => {
  const sampleSessions = {
    'session-1': {
      id: 'session-1',
      title: 'Patient Demographics Analysis',
      messages: [
        {
          id: 'welcome',
          type: 'bot',
          content: 'Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data, or any other database information.',
          timestamp: new Date('2025-07-20T10:00:00Z')
        },
        {
          id: 'msg-1',
          type: 'user',
          content: 'Show me patient demographics by age group',
          timestamp: new Date('2025-07-20T10:01:00Z')
        },
        {
          id: 'msg-2',
          type: 'bot',
          content: 'Here is the patient demographics analysis by age group.',
          sqlQuery: 'SELECT age_group, COUNT(*) as count FROM patients GROUP BY age_group',
          chartData: { type: 'bar', data: [10, 25, 30, 15, 8] },
          timestamp: new Date('2025-07-20T10:01:30Z')
        }
      ],
      createdAt: new Date('2025-07-20T10:00:00Z'),
      updatedAt: new Date('2025-07-20T10:01:30Z'),
      vannaId: 'vanna-1'
    },
    'session-2': {
      id: 'session-2',
      title: 'Doctor Specialties Report',
      messages: [
        {
          id: 'welcome',
          type: 'bot',
          content: 'Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data, or any other database information.',
          timestamp: new Date('2025-07-21T14:00:00Z')
        },
        {
          id: 'msg-1',
          type: 'user',
          content: 'List all doctors by specialty',
          timestamp: new Date('2025-07-21T14:01:00Z')
        },
        {
          id: 'msg-2',
          type: 'bot',
          content: 'Here are the doctors grouped by their specialties.',
          sqlQuery: 'SELECT specialty, COUNT(*) as doctor_count FROM doctors GROUP BY specialty',
          timestamp: new Date('2025-07-21T14:01:45Z')
        }
      ],
      createdAt: new Date('2025-07-21T14:00:00Z'),
      updatedAt: new Date('2025-07-21T14:01:45Z'),
      vannaId: 'vanna-2'
    },
    'session-3': {
      id: 'session-3',
      title: 'Consultation Trends',
      messages: [
        {
          id: 'welcome',
          type: 'bot',
          content: 'Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data, or any other database information.',
          timestamp: new Date('2025-07-22T09:00:00Z')
        },
        {
          id: 'msg-1',
          type: 'user',
          content: 'Show consultation trends over the last 6 months',
          timestamp: new Date('2025-07-22T09:01:00Z')
        },
        {
          id: 'msg-2',
          type: 'bot',
          content: 'Here are the consultation trends for the last 6 months.',
          sqlQuery: 'SELECT DATE_TRUNC(\'month\', consultation_date) as month, COUNT(*) as consultations FROM consultations WHERE consultation_date >= NOW() - INTERVAL \'6 months\' GROUP BY month ORDER BY month',
          chartData: { type: 'line', data: [120, 135, 150, 140, 160, 175] },
          timestamp: new Date('2025-07-22T09:02:00Z')
        },
        {
          id: 'msg-3',
          type: 'user',
          content: 'Can you create a chart for this data?',
          timestamp: new Date('2025-07-22T09:03:00Z')
        },
        {
          id: 'msg-4',
          type: 'bot',
          content: 'I\'ve created a line chart showing the consultation trends.',
          chartData: { type: 'line', data: [120, 135, 150, 140, 160, 175] },
          timestamp: new Date('2025-07-22T09:03:30Z')
        }
      ],
      createdAt: new Date('2025-07-22T09:00:00Z'),
      updatedAt: new Date('2025-07-22T09:03:30Z'),
      vannaId: 'vanna-3'
    },
    'session-4': {
      id: 'session-4',
      title: 'General Hospital Info',
      messages: [
        {
          id: 'welcome',
          type: 'bot',
          content: 'Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data, or any other database information.',
          timestamp: new Date('2025-07-22T08:00:00Z')
        },
        {
          id: 'msg-1',
          type: 'user',
          content: 'What services does the hospital provide?',
          timestamp: new Date('2025-07-22T08:01:00Z')
        },
        {
          id: 'msg-2',
          type: 'bot',
          content: 'Our hospital provides comprehensive medical services including emergency care, surgery, cardiology, neurology, pediatrics, and more.',
          timestamp: new Date('2025-07-22T08:01:15Z')
        }
      ],
      createdAt: new Date('2025-07-22T08:00:00Z'),
      updatedAt: new Date('2025-07-22T08:01:15Z'),
      vannaId: 'vanna-4'
    },
    'session-5': {
      id: 'session-5',
      title: 'Patient Search Query',
      messages: [
        {
          id: 'welcome',
          type: 'bot',
          content: 'Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data, or any other database information.',
          timestamp: new Date('2025-07-19T16:00:00Z')
        },
        {
          id: 'msg-1',
          type: 'user',
          content: 'Find all patients with diabetes',
          timestamp: new Date('2025-07-19T16:01:00Z')
        },
        {
          id: 'msg-2',
          type: 'bot',
          content: 'Here are all patients diagnosed with diabetes.',
          sqlQuery: 'SELECT * FROM patients WHERE diagnosis LIKE \'%diabetes%\'',
          timestamp: new Date('2025-07-19T16:01:20Z')
        }
      ],
      createdAt: new Date('2025-07-19T16:00:00Z'),
      updatedAt: new Date('2025-07-19T16:01:20Z'),
      vannaId: 'vanna-5'
    }
  };

  return sampleSessions;
};

export const loadSampleDataToLocalStorage = () => {
  const sampleSessions = generateSampleChatSessions();
  localStorage.setItem('chatSessions', JSON.stringify(sampleSessions));
  localStorage.setItem('currentSessionId', 'session-3');
  localStorage.setItem('sessionCounter', '6');
  
  console.log('Sample chat data loaded to localStorage');
  return sampleSessions;
};
